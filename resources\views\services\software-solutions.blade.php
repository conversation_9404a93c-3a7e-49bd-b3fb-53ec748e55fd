@extends('layout.App')
@section('content')
    <!-- <PERSON> Header Start -->
    <div class="page-header bg-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <!-- Page Header Box Start -->
                    <div class="page-header-box">
                        <h1 class="text-anime-style-2" data-cursor="-opaque">
                            <div style="position:relative;display:inline-block;">
                                <div
                                    style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                    S</div>
                                <div
                                    style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                    o</div>
                                <div
                                    style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                    f</div>
                                <div
                                    style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                    t</div>
                                <div
                                    style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                    w</div>
                                <div
                                    style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                    a</div>
                                <div
                                    style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                    r</div>
                                <div
                                    style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                    e</div>
                            </div> <span>
                                <div style="position:relative;display:inline-block;">
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        S</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        o</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        l</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        u</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        t</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        i</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        o</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        n</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        s</div>

                                </div>
                            </span>
                        </h1>

                    </div>
                    <!-- Page Header Box End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Page Service Single Start -->
    <div class="page-service-single">
        <div class="container">
            <div class="row">
                <x-service-sidebar />
                <div class="col-lg-8">
                    <!-- Service Single Content Start -->
                    <div class="service-single-content">
                        <!-- Service Feature Image Start -->
                        <div class="service-feature-image">
                            <figure class="image-anime reveal"
                                style="transform: translate(0px, 0px); opacity: 1; visibility: inherit;">
                                <img src="images/service-image-2.jpg" alt=""
                                    style="transform: translate(0px, 0px);">
                            </figure>
                        </div>
                        <!-- Service Feature Image End -->

                        <!-- Service Entry Start -->
                        <div class="service-entry">
                            <p class="wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">Custom-built,
                                secure, and scalable software that aligns with your workflows and growth strategy. We
                                modernise legacy systems and develop AI-enabled applications across multiple sectors,
                                ensuring your software solutions drive innovation and competitive advantage in today's
                                digital landscape.</p>

                            <p class="wow fadeInUp" data-wow-delay="0.2s"
                                style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInUp;">Our expertise
                                encompasses full-stack development, API integration, cloud-native applications, machine
                                learning implementations, and legacy system modernization. Whether you need custom business
                                applications, mobile solutions, or AI-powered platforms, our development team delivers
                                robust, maintainable software that scales with your business needs.</p>

                            <!-- Service Benefits Start -->
                            <div class="service-benefits">
                                <h2 class="text-anime-style-2">
                                    <div style="position:relative;display:inline-block;">
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            B</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            e</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            n</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            e</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            f</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            i</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            t</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            s</div>
                                    </div> <span>
                                        <div style="position:relative;display:inline-block;">
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                s</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                o</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                f</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                t</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                w</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                a</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                r</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                e</div>
                                        </div>
                                        <div style="position:relative;display:inline-block;">
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                s</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                o</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                l</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                u</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                t</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                i</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                o</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                n</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                s</div>
                                        </div>
                                    </span>
                                </h2>

                                <p class="wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">Software
                                    solutions streamline business processes, enhance productivity, and provide competitive
                                    advantages through custom automation, data insights, and scalable digital platforms
                                    tailored to your specific industry requirements.</p>

                                <!-- Service Benefit List Start -->
                                <div class="service-benefit-list wow fadeInUp" data-wow-delay="0.2s"
                                    style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInUp;">
                                    <ul>
                                        <li>custom applications &amp; workflow automation</li>
                                        <li>AI-powered features &amp; machine learning</li>
                                        <li>legacy system modernization &amp; integration</li>
                                        <li>scalable cloud-native architecture</li>
                                        <li>mobile &amp; cross-platform compatibility</li>
                                        <li>ongoing maintenance &amp; feature updates</li>
                                    </ul>
                                </div>
                                <!-- Service Benefit List End -->

                                <!-- Service Benefits Images Start -->
                                <div class="service-benefits-images">
                                    <!-- Service Benefits Img Start -->
                                    <div class="service-benefits-img">
                                        <figure class="image-anime reveal"
                                            style="transform: translate(0px, 0px); opacity: 1; visibility: inherit;">
                                            <img src="images/service-benefit-img-1.jpg" alt=""
                                                style="transform: translate(0px, 0px);">
                                        </figure>
                                    </div>
                                    <!-- Service Benefits Img End -->

                                    <!-- Service Benefits Img Start -->
                                    <div class="service-benefits-img">
                                        <figure class="image-anime reveal"
                                            style="transform: translate(0px, 0px); opacity: 1; visibility: inherit;">
                                            <img src="images/service-benefit-img-2.jpg" alt=""
                                                style="transform: translate(0px, 0px);">
                                        </figure>
                                    </div>
                                    <!-- Service Benefits Img End -->
                                </div>
                                <!-- Service Benefits Images End -->
                            </div>
                            <!-- Service Benefits End -->

                            <!-- Service Design Process Start -->
                            <div class="service-design-process">
                                <h2 class="text-anime-style-2">
                                    <div style="position:relative;display:inline-block;">
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            D</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            e</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            v</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            e</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            l</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            o</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            p</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            m</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            e</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            n</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            t</div>
                                    </div> <span>
                                        <div style="position:relative;display:inline-block;">
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                p</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                r</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                o</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                c</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                e</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                s</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                s</div>
                                        </div>
                                    </span>
                                </h2>

                                <p class="wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">Our
                                    development process follows agile methodologies, combining user-centered design,
                                    iterative development, and continuous testing to deliver high-quality software solutions
                                    that meet your exact specifications and business objectives.</p>

                                <!-- Design Process Item List Start -->
                                <div class="design-process-item-list">
                                    <!-- Design Process Item Start -->
                                    <div class="design-process-item wow fadeInUp" data-wow-delay="0.2s"
                                        style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInUp;">
                                        <div class="icon-box">
                                            <img src="images/icon-design-process-1.svg" alt="">
                                        </div>

                                        <div class="design-process-item-content">
                                            <h3>discovery</h3>
                                            <p>Requirements analysis &amp; technical architecture planning.</p>
                                        </div>
                                    </div>
                                    <!-- Design Process Item End -->

                                    <!-- Design Process Item Start -->
                                    <div class="design-process-item wow fadeInUp" data-wow-delay="0.4s"
                                        style="visibility: visible; animation-delay: 0.4s; animation-name: fadeInUp;">
                                        <div class="icon-box">
                                            <img src="images/icon-design-process-2.svg" alt="">
                                        </div>

                                        <div class="design-process-item-content">
                                            <h3>development</h3>
                                            <p>Agile development with continuous integration &amp; testing.</p>
                                        </div>
                                    </div>
                                    <!-- Design Process Item End -->

                                    <!-- Design Process Item Start -->
                                    <div class="design-process-item wow fadeInUp" data-wow-delay="0.6s"
                                        style="visibility: visible; animation-delay: 0.6s; animation-name: fadeInUp;">
                                        <div class="icon-box">
                                            <img src="images/icon-design-process-3.svg" alt="">
                                        </div>

                                        <div class="design-process-item-content">
                                            <h3>deployment</h3>
                                            <p>Seamless deployment &amp; ongoing maintenance support.</p>
                                        </div>
                                    </div>
                                    <!-- Design Process Item End -->
                                </div>
                                <!-- Design Process Item List End -->
                            </div>
                            <!-- Service Design Process End -->
                        </div>
                        <!-- Service Entry End -->

                        <!-- Page Single Faqs Start -->
                        <div class="page-single-faqs">
                            <!-- Section Title Start -->
                            <div class="section-title">
                                <h3 class="wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">FAQ's</h3>
                                <h2 class="text-anime-style-2" data-cursor="-opaque">
                                    <div style="position:relative;display:inline-block;">
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            F</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            i</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            n</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            d</div>
                                    </div>
                                    <div style="position:relative;display:inline-block;">
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            y</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            o</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            u</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            r</div>
                                    </div> <span>
                                        <div style="position:relative;display:inline-block;">
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                a</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                n</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                s</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                w</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                e</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                r</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                s</div>
                                        </div>
                                        <div style="position:relative;display:inline-block;">
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                h</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                e</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                r</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                e</div>
                                        </div>
                                    </span>
                                </h2>
                            </div>
                            <!-- Section Title End -->

                            <!-- FAQ Accordion Start -->
                            <div class="faq-accordion" id="accordion">
                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp"
                                    style="visibility: visible; animation-name: fadeInUp;">
                                    <h2 class="accordion-header" id="heading1">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#collapse1" aria-expanded="true" aria-controls="collapse1">
                                            What types of software solutions do you develop?
                                        </button>
                                    </h2>
                                    <div id="collapse1" class="accordion-collapse collapse show"
                                        aria-labelledby="heading1" data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p>We develop custom business applications, mobile apps, web platforms, API
                                                integrations, AI-powered solutions, and legacy system modernizations. Our
                                                expertise covers full-stack development across multiple technologies and
                                                industries.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->

                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp" data-wow-delay="0.2s"
                                    style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInUp;">
                                    <h2 class="accordion-header" id="heading2">
                                        <button class="accordion-button collapsed" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#collapse2" aria-expanded="false"
                                            aria-controls="collapse2">
                                            Do you provide AI and machine learning capabilities?
                                        </button>
                                    </h2>
                                    <div id="collapse2" class="accordion-collapse collapse" aria-labelledby="heading2"
                                        data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p>Yes, we integrate AI and machine learning features including predictive
                                                analytics, natural language processing, computer vision, and automated
                                                decision-making systems to enhance your software capabilities and business
                                                intelligence.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->

                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp" data-wow-delay="0.4s"
                                    style="visibility: visible; animation-delay: 0.4s; animation-name: fadeInUp;">
                                    <h2 class="accordion-header" id="heading3">
                                        <button class="accordion-button collapsed" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#collapse3" aria-expanded="false"
                                            aria-controls="collapse3">
                                            How do you handle legacy system modernization?
                                        </button>
                                    </h2>
                                    <div id="collapse3" class="accordion-collapse collapse" aria-labelledby="heading3"
                                        data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p>We assess existing systems, create migration strategies, and gradually
                                                modernize legacy applications while maintaining business continuity. Our
                                                approach includes API development, data migration, and phased implementation
                                                to minimize disruption.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->

                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp" data-wow-delay="0.6s"
                                    style="visibility: visible; animation-delay: 0.6s; animation-name: fadeInUp;">
                                    <h2 class="accordion-header" id="heading4">
                                        <button class="accordion-button collapsed" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#collapse4" aria-expanded="false"
                                            aria-controls="collapse4">
                                            What development methodologies do you use?
                                        </button>
                                    </h2>
                                    <div id="collapse4" class="accordion-collapse collapse" aria-labelledby="heading4"
                                        data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p>We follow agile development methodologies with continuous integration,
                                                automated testing, and regular client feedback cycles. This ensures rapid
                                                delivery, high quality, and alignment with your evolving business
                                                requirements.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->

                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp" data-wow-delay="0.8s"
                                    style="visibility: visible; animation-delay: 0.8s; animation-name: fadeInUp;">
                                    <h2 class="accordion-header" id="heading5">
                                        <button class="accordion-button collapsed" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#collapse5" aria-expanded="true"
                                            aria-controls="collapse5">
                                            Do you provide ongoing maintenance and support?
                                        </button>
                                    </h2>
                                    <div id="collapse5" class="accordion-collapse collapse" aria-labelledby="heading5"
                                        data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p>Yes, we provide comprehensive maintenance including bug fixes, security
                                                updates, performance optimization, feature enhancements, and technical
                                                support. Our maintenance plans ensure your software remains current and
                                                secure.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->
                            </div>
                            <!-- FAQ Accordion End -->
                        </div>
                        <!-- Page Single Faqs End -->
                    </div>
                    <!-- Service Single Content End -->
                </div>
            </div>
        </div>
    </div>
@endsection
