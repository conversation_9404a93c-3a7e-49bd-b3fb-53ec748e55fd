<?php

namespace Database\Seeders;

use App\Models\Post;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class PostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $posts = [
            [
                'title' => 'The Future of Cybersecurity in 2025',
                'content' => '<p>As we advance into 2025, the cybersecurity landscape continues to evolve at an unprecedented pace. Organizations worldwide are facing increasingly sophisticated threats that require innovative defense strategies.</p>

<h2>Emerging Threats</h2>
<p>The rise of AI-powered attacks has fundamentally changed how we approach cybersecurity. Threat actors are now leveraging machine learning algorithms to create more convincing phishing campaigns and to automate vulnerability discovery.</p>

<h2>Zero Trust Architecture</h2>
<p>Zero Trust has moved from a buzzword to a necessity. Organizations are implementing comprehensive Zero Trust frameworks that verify every user and device before granting access to network resources.</p>

<h2>Cloud Security Challenges</h2>
<p>With the continued migration to cloud services, securing multi-cloud environments has become a top priority. Organizations must balance accessibility with security in their cloud strategies.</p>

<p>The future of cybersecurity lies in proactive defense, continuous monitoring, and adaptive security measures that can evolve with the threat landscape.</p>',
                'image' => 'posts/cybersecurity-2025.jpg',
            ],
            [
                'title' => 'Digital Transformation: A Strategic Approach for Modern Businesses',
                'content' => '<p>Digital transformation is no longer optional for businesses looking to remain competitive in today\'s market. It\'s a strategic imperative that touches every aspect of an organization.</p>

<h2>Understanding Digital Transformation</h2>
<p>Digital transformation involves integrating digital technology into all areas of a business, fundamentally changing how you operate and deliver value to customers.</p>

<h2>Key Components</h2>
<ul>
<li>Process automation and optimization</li>
<li>Data-driven decision making</li>
<li>Customer experience enhancement</li>
<li>Employee empowerment through technology</li>
</ul>

<h2>Implementation Strategy</h2>
<p>Successful digital transformation requires a clear strategy, strong leadership commitment, and a culture that embraces change. Organizations must start with a comprehensive assessment of their current state and define clear objectives for their digital future.</p>

<blockquote>
<p>"Digital transformation is not about technology; it\'s about strategy and new ways of thinking." - Industry Expert</p>
</blockquote>

<p>The journey requires patience, investment, and a willingness to adapt as new technologies emerge and business needs evolve.</p>',
                'image' => 'posts/digital-transformation.jpg',
            ],
            [
                'title' => 'Building Resilient IT Infrastructure for Enterprise Success',
                'content' => '<p>In today\'s interconnected world, having a resilient IT infrastructure is crucial for enterprise success. Organizations need systems that can withstand disruptions while maintaining optimal performance.</p>

<h2>Core Principles of Resilient Infrastructure</h2>
<p>Resilient IT infrastructure is built on several key principles: redundancy, scalability, security, and monitoring. These elements work together to create systems that can adapt to changing demands and recover quickly from failures.</p>

<h2>Cloud-First Approach</h2>
<p>Modern enterprises are adopting cloud-first strategies that provide flexibility, scalability, and cost-effectiveness. Hybrid cloud solutions offer the best of both worlds, combining on-premises control with cloud agility.</p>

<h2>Disaster Recovery Planning</h2>
<p>A comprehensive disaster recovery plan is essential for business continuity. This includes regular backups, failover procedures, and testing protocols to ensure systems can be restored quickly in case of an incident.</p>

<h2>Monitoring and Maintenance</h2>
<p>Proactive monitoring helps identify potential issues before they become critical problems. Automated monitoring tools can track system performance, security threats, and capacity utilization in real-time.</p>

<p>Building resilient infrastructure is an ongoing process that requires continuous evaluation and improvement to meet evolving business needs.</p>',
                'image' => 'posts/it-infrastructure.jpg',
            ],
            [
                'title' => 'The Role of AI in Modern Software Development',
                'content' => '<p>Artificial Intelligence is revolutionizing software development, from code generation to testing and deployment. Developers are leveraging AI tools to increase productivity and improve code quality.</p>

<h2>AI-Powered Development Tools</h2>
<p>Modern IDEs now include AI assistants that can suggest code completions, identify bugs, and even generate entire functions based on natural language descriptions.</p>

<h2>Automated Testing</h2>
<p>AI is transforming software testing by automatically generating test cases, identifying edge cases, and predicting potential failure points in applications.</p>

<h2>Code Review and Quality Assurance</h2>
<p>Machine learning algorithms can analyze code patterns to identify potential security vulnerabilities, performance issues, and maintainability concerns.</p>

<h2>Future Implications</h2>
<p>As AI continues to evolve, we can expect even more sophisticated tools that will further streamline the development process while maintaining high standards of quality and security.</p>

<p>The integration of AI in software development is not about replacing developers, but about augmenting their capabilities and allowing them to focus on higher-level problem-solving and innovation.</p>',
                'image' => 'posts/ai-development.jpg',
            ],
            [
                'title' => 'Best Practices for Remote Team Management in Tech',
                'content' => '<p>Managing remote tech teams requires a different approach than traditional in-office management. Success depends on clear communication, proper tools, and trust-based relationships.</p>

<h2>Communication Strategies</h2>
<p>Effective communication is the foundation of successful remote teams. This includes regular check-ins, clear documentation, and the use of collaborative tools that keep everyone connected.</p>

<h2>Project Management Tools</h2>
<p>The right project management tools can make or break a remote team. Agile methodologies work particularly well for remote development teams, providing structure while maintaining flexibility.</p>

<h2>Building Team Culture</h2>
<p>Creating a strong team culture remotely requires intentional effort. Virtual team building activities, regular social interactions, and shared goals help maintain team cohesion.</p>

<h2>Performance Measurement</h2>
<p>Focus on outcomes rather than hours worked. Set clear expectations, define measurable goals, and provide regular feedback to ensure team members stay motivated and productive.</p>

<p>Remote work is here to stay, and organizations that master remote team management will have a significant competitive advantage in attracting and retaining top talent.</p>',
                'image' => 'posts/remote-management.jpg',
            ]
        ];

        foreach ($posts as $postData) {
            Post::create([
                'title' => $postData['title'],
                'slug' => Str::slug($postData['title']),
                'content' => $postData['content'],
                'image' => $postData['image'],
                'created_at' => now()->subDays(rand(1, 30)),
                'updated_at' => now()->subDays(rand(0, 5)),
            ]);
        }
    }
}
